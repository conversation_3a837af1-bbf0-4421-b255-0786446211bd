# Spring Boot Gateway 透明代理模块开发指南

## 项目概述

创建一个基于Maven和Spring Boot 2.7.7的透明代理网关模块，用于在前端和后端之间进行完全透明的API请求转发。该网关作为纯代理层，不修改任何请求和响应内容，不添加任何额外功能。

## 技术要求

### 基础框架
- **构建工具**: Maven
- **Spring Boot版本**: 2.7.7 (org.springframework.boot)
- **Java版本**: 8
- **项目类型**: Spring Boot应用

### 核心依赖
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-webflux</artifactId>
</dependency>
```

## 项目结构

```
gateway/
├── pom.xml
├── src/
│   └── main/
│       ├── java/
│       │   └── com/
│       │       └── example/
│       │           └── gateway/
│       │               ├── GatewayApplication.java
│       │               └── controller/
│       │                   └── ProxyController.java
│       └── resources/
│           ├── application.yml
│           └── application-dev.yml
```

## 功能需求

### 1. 透明代理转发

**架构流程**: 前端 → Gateway → 后端 → Gateway → 前端

**转发规则**:
- 接收路径：`http://gateway:8080/**`
- 转发目标：`http://backend-server/**`
- 完全保持原始路径结构

### 2. 完全透明转发

#### HTTP方法
- 保持原始HTTP方法不变（GET、POST、PUT、DELETE、PATCH等）

#### 请求头转发
- **完整转发**所有原始请求头
- **不修改**任何请求头内容
- **不添加**任何新的请求头
- 包括但不限于：Content-Type、Authorization、Cookie、User-Agent等

#### 请求体转发
- **原样转发**所有请求体内容
- 支持所有Content-Type：
  - application/json
  - application/x-www-form-urlencoded
  - multipart/form-data
  - text/plain
  - application/octet-stream
  - 其他任意格式

#### 响应转发
- **完整转发**后端响应状态码
- **完整转发**所有响应头
- **原样转发**响应体内容
- **不修改**任何响应数据

### 3. 错误处理
- 后端服务不可用：返回502 Bad Gateway
- 请求超时：返回504 Gateway Timeout
- 连接失败：返回502 Bad Gateway
- **不修改**后端返回的错误响应

## 透明代理要求

### 路径转发
- **完全保持**原始URL路径
- **不做**任何路径重写或映射
- **不做**HTTP方法转换

### 支持的API路径
基于提供的前端和后端API列表，Gateway需要透明转发所有以下路径：

#### 认证相关
- `/d/login`
- `/d/logout`
- `/d/auth`
- `/d/auth_status`

#### 业务功能
- `/d/event`
- `/d/event_feature`
- `/d/evidence`
- `/d/asset`
- `/d/statinfo`
- `/d/feature`
- `/d/topn`
- `/d/config`
- `/d/mo`
- `/d/sctl`

#### 工具接口
- `/d/geoinfo`
- `/d/portinfo`
- `/d/ipinfo`
- `/d/threatinfo`
- `/d/threatinfopro`
- `/d/locinfo`
- `/d/bwlist`
- `/d/internalip`

**重要**: Gateway对所有路径进行**完全透明**的转发，不区分具体的API功能

## 配置要求

### application.yml
```yaml
server:
  port: 8080

gateway:
  backend:
    base-url: http://localhost  # 后端服务地址
    timeout: 30000  # 请求超时时间(毫秒)

logging:
  level:
    com.example.gateway: INFO
```

### application-dev.yml
```yaml
gateway:
  backend:
    base-url: http://localhost  # 开发环境后端地址
```

## 实现要点

### 1. 完全透明转发
- **不做**任何HTTP方法转换
- **不做**任何请求参数转换
- **不做**任何请求体格式转换
- **不做**任何响应内容修改

### 2. 请求处理
- 原样转发HTTP方法（GET、POST、PUT、DELETE等）
- 原样转发所有查询参数
- 原样转发请求体（无论什么格式）
- 原样转发所有请求头

### 3. 响应处理
- 原样转发HTTP状态码
- 原样转发所有响应头
- 原样转发响应体内容
- 不添加任何额外的响应头

### 4. 异常处理
- 仅处理网络层面的异常（连接失败、超时等）
- 不处理业务层面的错误
- 将后端的所有错误响应原样返回给前端

## 代码实现建议

### ProxyController
- 使用`@RestController`注解
- 使用`@RequestMapping("/**")`捕获所有路径请求
- 使用`HttpServletRequest`和`HttpServletResponse`处理原始请求和响应
- 直接操作请求流和响应流，确保完全透明

### 核心实现逻辑
```java
@RequestMapping("/**")
public void proxy(HttpServletRequest request, HttpServletResponse response) {
    // 1. 获取原始请求的所有信息（方法、路径、参数、头、体）
    // 2. 构建到后端的相同请求
    // 3. 发送请求到后端
    // 4. 将后端响应原样写入前端响应
}
```

### 技术实现要点
- 使用`WebClient`或`RestTemplate`进行HTTP转发
- 使用`StreamUtils`处理请求体和响应体的流式传输
- 确保所有请求头和响应头的完整转发


### 网络架构
```
前端应用 → Gateway(8080) → 后端服务(80)
```

## 关键要求

1. **完全透明**: Gateway对请求和响应不做任何修改
2. **零业务逻辑**: 不包含任何业务相关的处理
3. **高性能**: 最小化转发延迟
4. **稳定可靠**: 处理网络异常，但不修改业务错误
5. **简单维护**: 代码结构简单清晰

## 验收标准

1. **透明性**: 前端通过Gateway访问后端与直接访问后端结果完全一致
2. **完整性**: 所有请求头、响应头、请求体、响应体完全保持原样
3. **兼容性**: 支持前端和后端的所有现有API
4. **性能**: Gateway引入的额外延迟<50ms
5. **稳定性**: 长时间运行无内存泄漏，正确处理网络异常
